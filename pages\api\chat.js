import { generateEmbeddings, generateChatResponse } from '../../lib/openai.js';
import { getPineconeIndex } from '../../lib/pinecone.js';

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { query } = req.body;

    console.log('🔄 Chat API called with query:', query);

    // Validate input
    if (!query || query.trim().length === 0) {
      return res.status(400).json({
        error: 'Query is required and cannot be empty'
      });
    }

    try {
      // Check if OpenAI API key is configured
      const apiKey = process.env.OPENAI_API_KEY;
      if (!apiKey || apiKey === 'your_openai_api_key_here') {
        console.log('OpenAI API key not configured, returning fallback response');
        return res.status(200).json({
          reply: 'Hello! I\'m VTU GPT. I\'m currently in demo mode as the OpenAI API key is not configured. Please contact the administrator to set up the API key for full functionality.'
        });
      }

      // Step 1: Generate embedding for the user query
      console.log('🔄 Generating query embedding...');
      const queryEmbedding = await generateEmbeddings(query);

      // Step 2: Search for relevant documents in Pinecone
      console.log('🔍 Searching for relevant documents...');
      const pineconeIndex = await getPineconeIndex();

      const searchResults = await pineconeIndex.query({
        vector: queryEmbedding,
        topK: 5, // Get top 5 most relevant chunks
        includeMetadata: true,
        includeValues: false
      });

      console.log(`📊 Found ${searchResults.matches.length} relevant document chunks`);

      // Step 3: Extract context from search results
      let context = '';
      const relevantChunks = [];

      if (searchResults.matches && searchResults.matches.length > 0) {
        for (const match of searchResults.matches) {
          if (match.score > 0.7) { // Only use highly relevant chunks
            context += `${match.metadata.text}\n\n`;
            relevantChunks.push({
              filename: match.metadata.filename,
              score: match.score,
              chunkId: match.metadata.chunkId
            });
          }
        }
      }

      // Step 4: Generate response using OpenAI with context
      let messages;

      if (context.trim().length > 0) {
        console.log(`🔄 Generating contextual response using ${context.length} characters of context...`);

        messages = [
          {
            role: 'system',
            content: `You are VTU GPT, an AI assistant for Vel Tech University (VTU). You help students and faculty with questions about VTU academics, procedures, and policies.

Use the following context from VTU documents to answer the user's question. If the context doesn't contain relevant information, say so and provide general guidance.

Context from VTU documents:
${context}

Instructions:
- Answer based primarily on the provided context
- Be helpful, accurate, and concise
- If the context doesn't fully answer the question, acknowledge this
- Maintain a friendly, professional tone
- Reference specific documents when possible`
          },
          {
            role: 'user',
            content: query
          }
        ];
      } else {
        console.log('🔄 No relevant context found, generating general response...');

        messages = [
          {
            role: 'system',
            content: `You are VTU GPT, an AI assistant for Vel Tech University (VTU). You help students and faculty with questions about VTU academics, procedures, and policies.

No specific documents were found for this query. Provide a helpful general response and suggest that the user:
1. Check the official VTU website
2. Contact their department
3. Ask the administrator to upload relevant documents

Maintain a friendly, professional tone.`
          },
          {
            role: 'user',
            content: query
          }
        ];
      }

      const response = await generateChatResponse(messages, {
        model: 'gpt-4o-mini',
        temperature: 0.7,
        maxTokens: 500
      });

      console.log('✅ Generated response successfully');

      // Return response with metadata
      return res.status(200).json({
        reply: response.content,
        metadata: {
          hasContext: context.trim().length > 0,
          relevantChunks: relevantChunks,
          contextLength: context.length,
          model: response.model,
          usage: response.usage
        }
      });

    } catch (error) {
      console.error('❌ Error in chat API:', error);

      // Return a fallback response on error
      return res.status(500).json({
        reply: 'I apologize, but I encountered an error while processing your question. Please try again later or contact the administrator if the problem persists.',
        error: error.message
      });
    }
  } else {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }
}
