{"compilerOptions": {"target": "es5", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "noEmit": true, "incremental": true, "module": "esnext", "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}