// scripts/test-setup.js
const nodemailer = require('nodemailer');

async function testSetup() {
  console.log('🔍 Testing VTU-GPT Authentication Setup...\n');

  // Load environment variables
  require('dotenv').config({ path: '.env.local' });

  let allTestsPassed = true;

  // Test 1: Environment Variables
  console.log('1️⃣ Testing Environment Variables...');
  const requiredEnvVars = [
    'JWT_SECRET',
    'EMAIL_HOST',
    'EMAIL_PORT',
    'EMAIL_USER',
    'EMAIL_PASS',
    'EMAIL_FROM',
    'APP_URL',
    'OPENAI_API_KEY',
    'PINECONE_API_KEY',
    'PINECONE_INDEX_NAME',
    'PINECONE_ENVIRONMENT'
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.log(`   ❌ Missing: ${envVar}`);
      allTestsPassed = false;
    } else {
      console.log(`   ✅ Found: ${envVar}`);
    }
  }

  // Test 2: Database Connection
  console.log('\n2️⃣ Testing SQLite Database Connection...');
  try {
    const sqlite3 = require('sqlite3');
    const { open } = require('sqlite');
    const path = require('path');
    const fs = require('fs');

    const dbPath = path.join(__dirname, '..', 'database', 'vtu_gpt.db');

    // Check if database file exists
    if (!fs.existsSync(dbPath)) {
      console.log('   ❌ Database file not found. Run: npm run setup-db');
      allTestsPassed = false;
    } else {
      const db = await open({
        filename: dbPath,
        driver: sqlite3.Database
      });

      const result = await db.get('SELECT datetime("now") as current_time');
      console.log('   ✅ Database connection successful');

      // Check if users table exists
      const tableCheck = await db.get(`
        SELECT name FROM sqlite_master
        WHERE type='table' AND name='users'
      `);

      if (tableCheck) {
        console.log('   ✅ Users table exists');
      } else {
        console.log('   ❌ Users table not found. Run: npm run setup-db');
        allTestsPassed = false;
      }

      await db.close();
    }
  } catch (error) {
    console.log(`   ❌ Database connection failed: ${error.message}`);
    allTestsPassed = false;
  }

  // Test 3: Email Configuration
  console.log('\n3️⃣ Testing Email Configuration...');
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.verify();
    console.log('   ✅ Email configuration valid');
  } catch (error) {
    console.log(`   ❌ Email configuration failed: ${error.message}`);
    console.log('   💡 Make sure you\'re using an App Password for Gmail');
    allTestsPassed = false;
  }

  // Test 4: Pinecone Connection
  console.log('\n4️⃣ Testing Pinecone Connection...');
  try {
    const { Pinecone } = require('@pinecone-database/pinecone');

    const pineconeClient = new Pinecone({
      apiKey: process.env.PINECONE_API_KEY,
    });

    const indexName = process.env.PINECONE_INDEX_NAME;
    const indexes = await pineconeClient.listIndexes();
    const indexExists = indexes.indexes?.some(idx => idx.name === indexName);

    if (indexExists) {
      console.log(`   ✅ Pinecone index "${indexName}" accessible`);

      const index = pineconeClient.index(indexName);
      const stats = await index.describeIndexStats();
      console.log(`   📊 Vector count: ${stats.totalRecordCount || 0}`);
    } else {
      console.log(`   ❌ Pinecone index "${indexName}" not found`);
      allTestsPassed = false;
    }
  } catch (error) {
    console.log(`   ❌ Pinecone connection failed: ${error.message}`);
    allTestsPassed = false;
  }

  // Test 5: OpenAI Connection
  console.log('\n5️⃣ Testing OpenAI Connection...');
  try {
    const OpenAI = require('openai');
    const openaiClient = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

    // Quick test with a simple embedding
    const testResponse = await openaiClient.embeddings.create({
      model: 'text-embedding-ada-002',
      input: 'test',
    });

    if (testResponse.data[0].embedding.length === 1536) {
      console.log('   ✅ OpenAI API key working correctly');
      console.log('   📊 Embedding dimensions: 1536');
    } else {
      console.log('   ❌ OpenAI API returned unexpected response');
      allTestsPassed = false;
    }
  } catch (error) {
    console.log(`   ❌ OpenAI connection failed: ${error.message}`);
    allTestsPassed = false;
  }

  // Test 6: JWT Secret
  console.log('\n6️⃣ Testing JWT Secret...');
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length >= 32) {
    console.log('   ✅ JWT secret is sufficiently long');
  } else {
    console.log('   ⚠️  JWT secret should be at least 32 characters long');
    console.log('   💡 Generate a secure random string for production');
  }

  // Summary
  console.log('\n📋 Setup Test Summary:');
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Your VTU-GPT RAG system is fully operational.');
    console.log('\n🚀 Next steps:');
    console.log('   1. Start the development server: npm run dev');
    console.log('   2. Visit http://localhost:3001');
    console.log('   3. Try registering with a @veltech.edu.in email');
    console.log('   4. Upload documents via admin dashboard (/admindashboard)');
    console.log('   5. Test RAG functionality with queries');
    console.log('   6. Monitor vector storage in Pinecone');
  } else {
    console.log('❌ Some tests failed. Please fix the issues above before proceeding.');
    console.log('\n📖 Check the README.md for detailed setup instructions.');
  }
}

if (require.main === module) {
  testSetup().catch(console.error);
}

module.exports = testSetup;
